import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/client';

export async function GET() {
  try {
    console.log('API Test: Starting properties fetch...');
    
    // Test basic connection
    const { data, error, count } = await supabase
      .from('properties')
      .select('*', { count: 'exact' });

    console.log('API Test: Query result:', { data, error, count });

    if (error) {
      console.error('API Test: Supabase error:', error);
      return NextResponse.json({ 
        success: false, 
        error: error.message,
        details: error 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      count: count || 0,
      properties: data || [],
      message: 'Properties fetched successfully'
    });

  } catch (err) {
    console.error('API Test: Unexpected error:', err);
    return NextResponse.json({ 
      success: false, 
      error: err instanceof Error ? err.message : 'Unknown error' 
    }, { status: 500 });
  }
}

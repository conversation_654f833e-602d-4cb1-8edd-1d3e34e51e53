'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { APP_CONFIG } from '@/constants/app';
import { Menu, X, Phone, Mail, Home, Building, Briefcase, User, FileText, MessageCircle, Sparkles } from 'lucide-react';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const navigation = [
    { name: 'Home', href: '/', icon: Home },
    { name: 'Properties', href: '/properties', icon: Building },
    { name: 'Services', href: '/services', icon: Briefcase },
    { name: 'About', href: '/about', icon: User },
    { name: 'Career', href: '/career', icon: Briefcase },
    { name: 'Blog', href: '/blog', icon: FileText },
    { name: 'Contact', href: '/contact', icon: MessageCircle },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-xl shadow-lg border-b border-gray-200/50'
        : 'bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-100/50'
    }`}>
      {/* Modern Top bar */}
      <div className="bg-gradient-to-r from-emerald-500/10 via-blue-500/10 to-purple-500/10 border-b border-white/20">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-12 text-sm">
            <div className="flex items-center space-x-6 text-gray-700">
              <div className="flex items-center space-x-2 hover:text-emerald-600 transition-colors">
                <div className="flex items-center justify-center w-6 h-6 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-full">
                  <Phone className="h-3 w-3 text-white" />
                </div>
                <span className="font-medium">{APP_CONFIG.phone}</span>
              </div>
              <div className="flex items-center space-x-2 hover:text-blue-600 transition-colors">
                <div className="flex items-center justify-center w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full">
                  <Mail className="h-3 w-3 text-white" />
                </div>
                <span className="font-medium">{APP_CONFIG.email}</span>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <Link href="/auth/login" className="text-gray-600 hover:text-emerald-600 font-medium transition-colors px-3 py-1 rounded-lg hover:bg-emerald-50">
                Login
              </Link>
              <Link href="/auth/register" className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-4 py-1 rounded-lg font-medium hover:from-emerald-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-sm">
                Register
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Main header */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Modern Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-105">
                <span className="text-white font-bold text-xl">D</span>
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                <Sparkles className="h-2 w-2 text-white" />
              </div>
            </div>
            <div className="hidden sm:block">
              <span className="font-bold text-2xl bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                {APP_CONFIG.name.split(' ')[0]}
              </span>
              <div className="text-sm text-gray-600 font-medium -mt-1">
                {APP_CONFIG.name.split(' ').slice(1).join(' ')}
              </div>
            </div>
          </Link>

          {/* Modern Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1 bg-white/50 backdrop-blur-sm rounded-2xl p-2 shadow-sm border border-gray-200/50">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-2 px-4 py-2 rounded-xl text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500 hover:to-blue-500 font-medium transition-all duration-300 transform hover:scale-105 group"
                >
                  <Icon className="h-4 w-4 group-hover:text-white transition-colors" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Modern CTA Button */}
          <div className="hidden md:flex items-center space-x-4">
            <Button asChild className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white border-0 rounded-xl px-6 py-3 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <Link href="/properties" className="flex items-center space-x-2">
                <Building className="h-4 w-4" />
                <span>View Properties</span>
              </Link>
            </Button>
          </div>

          {/* Modern Mobile menu button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-3 rounded-xl bg-white/50 backdrop-blur-sm border border-gray-200/50 text-gray-700 hover:bg-gradient-to-r hover:from-emerald-500 hover:to-blue-500 hover:text-white transition-all duration-300 transform hover:scale-105"
          >
            {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t bg-white">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-gray-700 hover:text-primary font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <div className="border-t pt-2 mt-2">
                <Link
                  href="/auth/login"
                  className="block px-3 py-2 text-gray-700 hover:text-primary"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Login
                </Link>
                <Link
                  href="/auth/register"
                  className="block px-3 py-2 text-gray-700 hover:text-primary"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Register
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}

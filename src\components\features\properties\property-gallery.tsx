'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Maximize, Play } from 'lucide-react';

interface PropertyGalleryProps {
  propertyId: string;
}

export function PropertyGallery({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  propertyId
}: PropertyGalleryProps) {
  const [currentImage, setCurrentImage] = useState(0);
  
  // Mock images - will be replaced with real data
  const images = [
    '/images/property-1.jpg',
    '/images/property-2.jpg',
    '/images/property-3.jpg',
    '/images/property-4.jpg',
  ];

  const nextImage = () => {
    setCurrentImage((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImage((prev) => (prev - 1 + images.length) % images.length);
  };

  return (
    <div className="space-y-4">
      {/* Main Image */}
      <div className="relative aspect-[16/10] bg-gray-200 rounded-lg overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center text-gray-400">
          <div className="text-center">
            <Maximize className="h-16 w-16 mx-auto mb-2" />
            <p>Property Image {currentImage + 1}</p>
          </div>
        </div>
        
        {/* Navigation Arrows */}
        <Button
          variant="secondary"
          size="icon"
          className="absolute left-4 top-1/2 transform -translate-y-1/2"
          onClick={prevImage}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="secondary"
          size="icon"
          className="absolute right-4 top-1/2 transform -translate-y-1/2"
          onClick={nextImage}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* Action Buttons */}
        <div className="absolute top-4 right-4 flex gap-2">
          <Button variant="secondary" size="sm">
            <Play className="h-4 w-4 mr-2" />
            Virtual Tour
          </Button>
          <Button variant="secondary" size="sm">
            <Maximize className="h-4 w-4 mr-2" />
            View All
          </Button>
        </div>

        {/* Image Counter */}
        <div className="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
          {currentImage + 1} / {images.length}
        </div>
      </div>

      {/* Thumbnail Grid */}
      <div className="grid grid-cols-4 gap-2">
        {images.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentImage(index)}
            className={`aspect-square bg-gray-200 rounded-md overflow-hidden border-2 transition-colors ${
              currentImage === index ? 'border-primary' : 'border-transparent'
            }`}
          >
            <div className="w-full h-full flex items-center justify-center text-gray-400">
              <span className="text-xs">Img {index + 1}</span>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}

import { PropertyDetails } from '@/components/features/properties/property-details';
import { PropertyGallery } from '@/components/features/properties/property-gallery';
import { PropertyInquiryForm } from '@/components/features/properties/property-inquiry-form';
import { SimilarProperties } from '@/components/features/properties/similar-properties';

interface PropertyPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PropertyPage({ params }: PropertyPageProps) {
  const { id } = await params;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <PropertyGallery propertyId={id} />
          <PropertyDetails propertyId={id} />
        </div>
        <div className="lg:col-span-1">
          <PropertyInquiryForm propertyId={id} />
        </div>
      </div>

      <div className="mt-12">
        <SimilarProperties propertyId={id} />
      </div>
    </div>
  );
}

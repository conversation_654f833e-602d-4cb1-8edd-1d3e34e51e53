import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Users, 
  Award, 
  Globe, 
  Heart,
  Target,
  Eye,
  Linkedin,
  Mail
} from 'lucide-react';
import Link from 'next/link';

const stats = [
  { label: 'Years of Experience', value: '15+' },
  { label: 'Properties Sold', value: '500+' },
  { label: 'Happy Clients', value: '1000+' },
  { label: 'Cities Covered', value: '12' },
];

const values = [
  {
    icon: Heart,
    title: 'Client-Centric',
    description: 'We put our clients first, ensuring their needs and goals are our top priority.',
  },
  {
    icon: Award,
    title: 'Excellence',
    description: 'We strive for excellence in every transaction and interaction.',
  },
  {
    icon: Users,
    title: 'Integrity',
    description: 'We conduct business with honesty, transparency, and ethical practices.',
  },
  {
    icon: Globe,
    title: 'Innovation',
    description: 'We embrace technology and innovative solutions to serve our clients better.',
  },
];

const team = [
  {
    name: '<PERSON>',
    role: 'Founder & CEO',
    bio: 'With over 15 years in Moroccan real estate, <PERSON> founded Darden PM to revolutionize property services.',
    image: '/team/ahmed.jpg',
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Head of Sales',
    bio: 'Fatima leads our sales team with expertise in luxury properties and international clients.',
    image: '/team/fatima.jpg',
  },
  {
    name: 'Youssef Mansouri',
    role: 'Property Manager',
    bio: 'Youssef oversees our property management division with a focus on client satisfaction.',
    image: '/team/youssef.jpg',
  },
  {
    name: 'Sarah El Fassi',
    role: 'Marketing Director',
    bio: 'Sarah drives our marketing initiatives and digital presence across Morocco.',
    image: '/team/sarah.jpg',
  },
];

export default function AboutPage() {
  return (
    <div className="py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">About Us</Badge>
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Your Trusted Real Estate Partner in Morocco
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Since 2009, Darden Property & Management has been helping clients navigate 
            Morocco&apos;s dynamic real estate market with expertise, integrity, and innovation.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-primary mb-2">
                {stat.value}
              </div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          <div>
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Our Story
            </h2>
            <div className="space-y-4 text-gray-600">
              <p>
                Founded in 2009 by Ahmed Benali, Darden Property & Management began as a 
                small real estate agency in Casablanca with a simple mission: to provide 
                exceptional service to property buyers, sellers, and investors in Morocco.
              </p>
              <p>
                Over the years, we&apos;ve grown into one of Morocco&apos;s most trusted real estate
                companies, expanding our services to include property management, investment 
                consulting, and comprehensive real estate solutions across 12 major cities.
              </p>
              <p>
                Today, as Morocco prepares to co-host the FIFA World Cup 2030 and the CAF 
                Africa Cup of Nations 2025, we&apos;re at the forefront of the country&apos;s real
                estate boom, helping clients capitalize on unprecedented opportunities.
              </p>
            </div>
          </div>
          <div className="aspect-[4/3] bg-gray-200 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-400">
              <Users className="h-16 w-16 mx-auto mb-4" />
              <p>Company Story Image</p>
            </div>
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
          <Card className="p-8">
            <CardContent className="p-0">
              <div className="flex items-center mb-4">
                <Target className="h-8 w-8 text-primary mr-3" />
                <h3 className="text-2xl font-bold text-gray-900">Our Mission</h3>
              </div>
              <p className="text-gray-600">
                To empower our clients with expert knowledge, innovative solutions, and 
                personalized service that transforms their real estate dreams into reality 
                while contributing to Morocco&apos;s economic growth.
              </p>
            </CardContent>
          </Card>

          <Card className="p-8">
            <CardContent className="p-0">
              <div className="flex items-center mb-4">
                <Eye className="h-8 w-8 text-primary mr-3" />
                <h3 className="text-2xl font-bold text-gray-900">Our Vision</h3>
              </div>
              <p className="text-gray-600">
                To be Morocco&apos;s leading real estate company, recognized for our integrity,
                innovation, and commitment to excellence, while setting new standards for 
                the industry across North Africa.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Values */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Values
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              These core values guide everything we do and shape our relationships 
              with clients, partners, and communities.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <Card key={index} className="text-center p-6">
                  <CardContent className="p-0">
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {value.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Team */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Meet Our Team
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our experienced team of real estate professionals is dedicated to 
              providing exceptional service and expertise.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-12 w-12 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {member.name}
                  </h3>
                  <div className="text-primary font-medium mb-3">
                    {member.role}
                  </div>
                  <p className="text-gray-600 text-sm mb-4">
                    {member.bio}
                  </p>
                  <div className="flex justify-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Linkedin className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Mail className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center bg-gray-50 rounded-2xl p-8 lg:p-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Ready to Work With Us?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of satisfied clients who have trusted us with their 
            real estate needs. Let&apos;s make your property dreams a reality.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/contact">Contact Us Today</Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/properties">View Properties</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

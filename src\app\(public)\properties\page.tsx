'use client';

import { useState, useEffect } from 'react';
import { PropertySearch, type SearchFilters } from '@/components/features/properties/property-search';
import { PropertyGrid } from '@/components/features/properties/property-grid';
import { useProperties } from '@/hooks/use-properties';

export default function PropertiesPage() {
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});

  const {
    properties,
    loading,
    error,
    totalCount
  } = useProperties({
    ...searchFilters,
    limit: 12
  });

  // Debug logging
  useEffect(() => {
    console.log('Properties Page Debug:', {
      properties,
      loading,
      error,
      totalCount,
      searchFilters
    });
  }, [properties, loading, error, totalCount, searchFilters]);

  const handleSearch = (filters: SearchFilters) => {
    setSearchFilters(filters);
  };

  const handleReset = () => {
    setSearchFilters({});
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4 italian-flag-corner">
          Find Your Perfect Property
        </h1>
        <p className="text-gray-600 max-w-2xl">
          Discover exceptional properties across Morocco. From luxury villas in Casablanca
          to traditional riads in Marrakech, find your dream home with us.
        </p>
      </div>

      <PropertySearch onSearch={handleSearch} onReset={handleReset} />

      <div className="mt-8">
        <PropertyGrid
          properties={properties}
          loading={loading}
          error={error}
          totalCount={totalCount}
        />
      </div>
    </div>
  );
}

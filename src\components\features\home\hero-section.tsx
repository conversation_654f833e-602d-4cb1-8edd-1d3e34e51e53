'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, MapPin } from 'lucide-react';
import Link from 'next/link';

export function HeroSection() {
  return (
    <section className="relative bg-gradient-to-r from-gray-900 to-gray-700 text-white">
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="relative container mx-auto px-4 py-24 lg:py-32">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            Find Your Perfect Property in{' '}
            <span className="text-primary">Morocco</span>
          </h1>
          <p className="text-xl lg:text-2xl text-gray-200 mb-8 max-w-2xl mx-auto">
            Discover exceptional properties across Morocco&apos;s most desirable locations.
            From luxury villas to traditional riads, your dream home awaits.
          </p>

          {/* Search Bar */}
          <div className="bg-white rounded-lg p-4 shadow-xl max-w-2xl mx-auto mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Search by location, property type..."
                  className="pl-10 border-0 text-gray-900 placeholder:text-gray-500"
                />
              </div>
              <div className="flex-1 relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="City, Region..."
                  className="pl-10 border-0 text-gray-900 placeholder:text-gray-500"
                />
              </div>
              <Button size="lg" className="px-8">
                Search Properties
              </Button>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/properties">Browse All Properties</Link>
            </Button>
            <Button size="lg" variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-gray-900" asChild>
              <Link href="/services">Our Services</Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('/patterns/moroccan-pattern.svg')] bg-repeat"></div>
      </div>
    </section>
  );
}
